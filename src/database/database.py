"""
数据库管理器

支持SQLite数据库和JSON文件存储
"""

import sqlite3
import json
import asyncio
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Union
from decimal import Decimal
import structlog

from .models import (
    TradeRecord, PriceRecord, SpreadRecord, RiskMetrics,
    SystemStatus, PerformanceMetrics
)

logger = structlog.get_logger(__name__)


class DatabaseManager:
    """数据库管理器"""

    def __init__(self, db_path: str = "data/arbitrage.db", backup_dir: str = "data/backups"):
        """
        初始化数据库管理器

        Args:
            db_path: SQLite数据库文件路径
            backup_dir: 备份目录
        """
        self.db_path = Path(db_path)
        self.backup_dir = Path(backup_dir)
        self.connection: Optional[sqlite3.Connection] = None

        # 确保目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.backup_dir.mkdir(parents=True, exist_ok=True)

        # 数据缓存
        self._cache = {
            'trades': [],
            'prices': [],
            'spreads': [],
            'risk_metrics': [],
            'system_status': [],
            'performance': []
        }

        # 缓存设置
        self.cache_size = 1000
        self.auto_commit_interval = 60  # 秒

    async def initialize(self) -> None:
        """初始化数据库"""
        try:
            # 创建数据库连接
            self.connection = sqlite3.connect(
                str(self.db_path),
                check_same_thread=False
            )
            self.connection.row_factory = sqlite3.Row

            # 创建表
            await self._create_tables()

            # 启动自动提交任务
            asyncio.create_task(self._auto_commit_task())

            logger.info("数据库初始化完成", db_path=str(self.db_path))

        except Exception as e:
            logger.error("数据库初始化失败", error=str(e))
            raise

    async def _create_tables(self) -> None:
        """创建数据库表"""
        cursor = self.connection.cursor()

        # 交易记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trades (
                id TEXT PRIMARY KEY,
                timestamp TEXT NOT NULL,
                symbol TEXT NOT NULL,
                trade_type TEXT NOT NULL,
                binance_order_id TEXT,
                binance_side TEXT,
                binance_price REAL,
                binance_quantity REAL,
                binance_status TEXT,
                binance_fee REAL,
                binance_timestamp TEXT,
                lighter_order_id TEXT,
                lighter_side TEXT,
                lighter_price REAL,
                lighter_quantity REAL,
                lighter_status TEXT,
                lighter_fee REAL,
                lighter_timestamp TEXT,
                expected_profit REAL,
                actual_profit REAL,
                profit_rate REAL,
                spread_at_entry REAL,
                spread_at_exit REAL,
                execution_time_ms INTEGER,
                slippage REAL,
                status TEXT NOT NULL,
                error_message TEXT,
                position_size REAL,
                risk_score REAL
            )
        """)

        # 价格记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS prices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                symbol TEXT NOT NULL,
                exchange TEXT NOT NULL,
                bid_price REAL NOT NULL,
                ask_price REAL NOT NULL,
                last_price REAL NOT NULL,
                volume_24h REAL,
                bid_quantity REAL,
                ask_quantity REAL,
                high_24h REAL,
                low_24h REAL,
                change_24h REAL
            )
        """)

        # 价差记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS spreads (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                symbol TEXT NOT NULL,
                binance_bid REAL NOT NULL,
                binance_ask REAL NOT NULL,
                lighter_bid REAL NOT NULL,
                lighter_ask REAL NOT NULL,
                bid_spread REAL NOT NULL,
                ask_spread REAL NOT NULL,
                mid_spread REAL NOT NULL,
                ma_spread REAL,
                signal TEXT,
                signal_strength REAL
            )
        """)

        # 风险指标表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS risk_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                total_position_size REAL NOT NULL,
                position_limit REAL NOT NULL,
                position_utilization REAL NOT NULL,
                total_pnl REAL NOT NULL,
                daily_pnl REAL NOT NULL,
                max_drawdown REAL NOT NULL,
                unrealized_pnl REAL NOT NULL,
                active_trades_count INTEGER NOT NULL,
                pending_orders_count INTEGER NOT NULL,
                failed_trades_count INTEGER NOT NULL,
                success_rate REAL NOT NULL,
                spread_volatility REAL NOT NULL,
                price_volatility REAL NOT NULL,
                liquidity_risk REAL NOT NULL,
                risk_score REAL NOT NULL,
                risk_level TEXT NOT NULL,
                position_limit_hit BOOLEAN,
                daily_loss_limit_hit BOOLEAN,
                max_drawdown_limit_hit BOOLEAN,
                trading_halted BOOLEAN
            )
        """)

        # 系统状态表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                is_running BOOLEAN NOT NULL,
                is_trading_enabled BOOLEAN NOT NULL,
                is_paper_trading BOOLEAN NOT NULL,
                binance_connected BOOLEAN NOT NULL,
                lighter_connected BOOLEAN NOT NULL,
                websocket_connected BOOLEAN NOT NULL,
                cpu_usage REAL NOT NULL,
                memory_usage REAL NOT NULL,
                network_latency_ms REAL NOT NULL,
                last_price_update TEXT NOT NULL,
                last_trade_execution TEXT,
                last_error TEXT,
                error_count INTEGER NOT NULL
            )
        """)

        # 性能指标表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                period TEXT NOT NULL,
                total_trades INTEGER NOT NULL,
                successful_trades INTEGER NOT NULL,
                failed_trades INTEGER NOT NULL,
                success_rate REAL NOT NULL,
                total_profit REAL NOT NULL,
                total_loss REAL NOT NULL,
                net_profit REAL NOT NULL,
                profit_factor REAL NOT NULL,
                sharpe_ratio REAL NOT NULL,
                max_drawdown REAL NOT NULL,
                calmar_ratio REAL NOT NULL,
                avg_trade_duration_seconds REAL NOT NULL,
                avg_profit_per_trade REAL NOT NULL,
                avg_execution_time_ms REAL NOT NULL
            )
        """)

        # 创建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(timestamp)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trades_symbol ON trades(symbol)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_prices_timestamp ON prices(timestamp)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_prices_symbol_exchange ON prices(symbol, exchange)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_spreads_timestamp ON spreads(timestamp)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_spreads_symbol ON spreads(symbol)")

        self.connection.commit()

    async def save_trade(self, trade: TradeRecord) -> None:
        """保存交易记录"""
        try:
            # 添加到缓存
            self._cache['trades'].append(trade)

            # 如果缓存满了，立即写入数据库
            if len(self._cache['trades']) >= self.cache_size:
                await self._flush_trades()

        except Exception as e:
            logger.error("保存交易记录失败", error=str(e), trade_id=trade.id)

    async def save_price(self, price: PriceRecord) -> None:
        """保存价格记录"""
        try:
            self._cache['prices'].append(price)

            if len(self._cache['prices']) >= self.cache_size:
                await self._flush_prices()

        except Exception as e:
            logger.error("保存价格记录失败", error=str(e))

    async def save_spread(self, spread: SpreadRecord) -> None:
        """保存价差记录"""
        try:
            self._cache['spreads'].append(spread)

            if len(self._cache['spreads']) >= self.cache_size:
                await self._flush_spreads()

        except Exception as e:
            logger.error("保存价差记录失败", error=str(e))

    async def save_risk_metrics(self, metrics: RiskMetrics) -> None:
        """保存风险指标"""
        try:
            self._cache['risk_metrics'].append(metrics)

            if len(self._cache['risk_metrics']) >= 100:  # 风险指标更频繁地写入
                await self._flush_risk_metrics()

        except Exception as e:
            logger.error("保存风险指标失败", error=str(e))

    async def save_system_status(self, status: SystemStatus) -> None:
        """保存系统状态"""
        try:
            self._cache['system_status'].append(status)

            if len(self._cache['system_status']) >= 100:
                await self._flush_system_status()

        except Exception as e:
            logger.error("保存系统状态失败", error=str(e))

    async def save_performance_metrics(self, metrics: PerformanceMetrics) -> None:
        """保存性能指标"""
        try:
            self._cache['performance'].append(metrics)
            await self._flush_performance()  # 性能指标立即写入

        except Exception as e:
            logger.error("保存性能指标失败", error=str(e))

    async def get_recent_trades(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取最近的交易记录（包括缓存中的数据）"""
        try:
            # 获取缓存中的交易记录
            cached_trades = [trade.to_dict() for trade in self._cache['trades']]

            # 设置row_factory以便返回字典
            self.connection.row_factory = sqlite3.Row
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT * FROM trades
                ORDER BY timestamp DESC
                LIMIT ?
            """, (limit,))

            rows = cursor.fetchall()
            # 重置row_factory
            self.connection.row_factory = None
            db_trades = [dict(row) for row in rows]

            # 合并缓存和数据库中的交易记录
            all_trades = cached_trades + db_trades

            # 按时间戳排序并去重（基于ID）
            seen_ids = set()
            unique_trades = []

            # 按时间戳降序排序
            all_trades.sort(key=lambda x: x['timestamp'], reverse=True)

            for trade in all_trades:
                if trade['id'] not in seen_ids:
                    seen_ids.add(trade['id'])
                    unique_trades.append(trade)

                # 限制返回数量
                if len(unique_trades) >= limit:
                    break

            return unique_trades

        except Exception as e:
            logger.error("获取交易记录失败", error=str(e))
            return []

    async def get_trades_by_date(self, date: datetime) -> List[Dict[str, Any]]:
        """获取指定日期的交易记录"""
        try:
            start_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = start_date + timedelta(days=1)

            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT * FROM trades
                WHERE timestamp >= ? AND timestamp < ?
                ORDER BY timestamp DESC
            """, (start_date.isoformat(), end_date.isoformat()))

            rows = cursor.fetchall()
            return [dict(row) for row in rows]

        except Exception as e:
            logger.error("获取日期交易记录失败", error=str(e), date=date.isoformat())
            return []

    async def get_price_history(self, symbol: str, exchange: str, hours: int = 24) -> List[Dict[str, Any]]:
        """获取价格历史"""
        try:
            start_time = datetime.utcnow() - timedelta(hours=hours)

            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT * FROM prices
                WHERE symbol = ? AND exchange = ? AND timestamp >= ?
                ORDER BY timestamp ASC
            """, (symbol, exchange, start_time.isoformat()))

            rows = cursor.fetchall()
            return [dict(row) for row in rows]

        except Exception as e:
            logger.error("获取价格历史失败", error=str(e))
            return []

    async def get_spread_history(self, symbol: str, hours: int = 24) -> List[Dict[str, Any]]:
        """获取价差历史"""
        try:
            start_time = datetime.utcnow() - timedelta(hours=hours)

            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT * FROM spreads
                WHERE symbol = ? AND timestamp >= ?
                ORDER BY timestamp ASC
            """, (symbol, start_time.isoformat()))

            rows = cursor.fetchall()
            return [dict(row) for row in rows]

        except Exception as e:
            logger.error("获取价差历史失败", error=str(e))
            return []

    async def get_latest_risk_metrics(self) -> Optional[Dict[str, Any]]:
        """获取最新的风险指标"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT * FROM risk_metrics
                ORDER BY timestamp DESC
                LIMIT 1
            """)

            row = cursor.fetchone()
            return dict(row) if row else None

        except Exception as e:
            logger.error("获取风险指标失败", error=str(e))
            return None

    async def cleanup_old_data(self, days: int = 30) -> None:
        """清理旧数据"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            cursor = self.connection.cursor()

            # 清理旧的价格记录
            cursor.execute("DELETE FROM prices WHERE timestamp < ?", (cutoff_date.isoformat(),))

            # 清理旧的价差记录
            cursor.execute("DELETE FROM spreads WHERE timestamp < ?", (cutoff_date.isoformat(),))

            # 清理旧的系统状态记录
            cursor.execute("DELETE FROM system_status WHERE timestamp < ?", (cutoff_date.isoformat(),))

            # 保留所有交易记录和风险指标（用于历史分析）

            self.connection.commit()
            logger.info("清理旧数据完成", cutoff_date=cutoff_date.isoformat())

        except Exception as e:
            logger.error("清理旧数据失败", error=str(e))

    async def backup_database(self) -> str:
        """备份数据库"""
        try:
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            backup_path = self.backup_dir / f"arbitrage_backup_{timestamp}.db"

            # 复制数据库文件
            import shutil
            shutil.copy2(self.db_path, backup_path)

            logger.info("数据库备份完成", backup_path=str(backup_path))
            return str(backup_path)

        except Exception as e:
            logger.error("数据库备份失败", error=str(e))
            raise

    async def _flush_trades(self) -> None:
        """刷新交易记录到数据库"""
        if not self._cache['trades']:
            return

        try:
            cursor = self.connection.cursor()
            for trade in self._cache['trades']:
                trade_data = trade.to_dict()

                cursor.execute("""
                    INSERT OR REPLACE INTO trades VALUES (
                        :id, :timestamp, :symbol, :trade_type,
                        :binance_order_id, :binance_side, :binance_price, :binance_quantity, :binance_status, :binance_fee, :binance_timestamp,
                        :lighter_order_id, :lighter_side, :lighter_price, :lighter_quantity, :lighter_status, :lighter_fee, :lighter_timestamp,
                        :expected_profit, :actual_profit, :profit_rate, :spread_at_entry, :spread_at_exit,
                        :execution_time_ms, :slippage, :status, :error_message, :position_size, :risk_score
                    )
                """, trade_data)

            self.connection.commit()
            self._cache['trades'].clear()

        except Exception as e:
            logger.error("刷新交易记录失败", error=str(e))

    async def _flush_prices(self) -> None:
        """刷新价格记录到数据库"""
        if not self._cache['prices']:
            return

        try:
            cursor = self.connection.cursor()
            for price in self._cache['prices']:
                price_data = price.to_dict()

                cursor.execute("""
                    INSERT INTO prices (
                        timestamp, symbol, exchange, bid_price, ask_price, last_price,
                        volume_24h, bid_quantity, ask_quantity, high_24h, low_24h, change_24h
                    ) VALUES (
                        :timestamp, :symbol, :exchange, :bid_price, :ask_price, :last_price,
                        :volume_24h, :bid_quantity, :ask_quantity, :high_24h, :low_24h, :change_24h
                    )
                """, price_data)

            self.connection.commit()
            self._cache['prices'].clear()

        except Exception as e:
            logger.error("刷新价格记录失败", error=str(e))

    async def _flush_spreads(self) -> None:
        """刷新价差记录到数据库"""
        if not self._cache['spreads']:
            return

        try:
            cursor = self.connection.cursor()
            for spread in self._cache['spreads']:
                spread_data = spread.to_dict()

                cursor.execute("""
                    INSERT INTO spreads (
                        timestamp, symbol, binance_bid, binance_ask, lighter_bid, lighter_ask,
                        bid_spread, ask_spread, mid_spread, ma_spread, signal, signal_strength
                    ) VALUES (
                        :timestamp, :symbol, :binance_bid, :binance_ask, :lighter_bid, :lighter_ask,
                        :bid_spread, :ask_spread, :mid_spread, :ma_spread, :signal, :signal_strength
                    )
                """, spread_data)

            self.connection.commit()
            self._cache['spreads'].clear()

        except Exception as e:
            logger.error("刷新价差记录失败", error=str(e))

    async def _flush_risk_metrics(self) -> None:
        """刷新风险指标到数据库"""
        if not self._cache['risk_metrics']:
            return

        try:
            cursor = self.connection.cursor()
            for metrics in self._cache['risk_metrics']:
                metrics_data = metrics.to_dict()

                cursor.execute("""
                    INSERT INTO risk_metrics (
                        timestamp, total_position_size, position_limit, position_utilization,
                        total_pnl, daily_pnl, max_drawdown, unrealized_pnl,
                        active_trades_count, pending_orders_count, failed_trades_count, success_rate,
                        spread_volatility, price_volatility, liquidity_risk,
                        risk_score, risk_level, position_limit_hit, daily_loss_limit_hit,
                        max_drawdown_limit_hit, trading_halted
                    ) VALUES (
                        :timestamp, :total_position_size, :position_limit, :position_utilization,
                        :total_pnl, :daily_pnl, :max_drawdown, :unrealized_pnl,
                        :active_trades_count, :pending_orders_count, :failed_trades_count, :success_rate,
                        :spread_volatility, :price_volatility, :liquidity_risk,
                        :risk_score, :risk_level, :position_limit_hit, :daily_loss_limit_hit,
                        :max_drawdown_limit_hit, :trading_halted
                    )
                """, metrics_data)

            self.connection.commit()
            self._cache['risk_metrics'].clear()

        except Exception as e:
            logger.error("刷新风险指标失败", error=str(e))

    async def _flush_system_status(self) -> None:
        """刷新系统状态到数据库"""
        if not self._cache['system_status']:
            return

        try:
            cursor = self.connection.cursor()
            for status in self._cache['system_status']:
                status_data = status.to_dict()

                cursor.execute("""
                    INSERT INTO system_status (
                        timestamp, is_running, is_trading_enabled, is_paper_trading,
                        binance_connected, lighter_connected, websocket_connected,
                        cpu_usage, memory_usage, network_latency_ms,
                        last_price_update, last_trade_execution, last_error, error_count
                    ) VALUES (
                        :timestamp, :is_running, :is_trading_enabled, :is_paper_trading,
                        :binance_connected, :lighter_connected, :websocket_connected,
                        :cpu_usage, :memory_usage, :network_latency_ms,
                        :last_price_update, :last_trade_execution, :last_error, :error_count
                    )
                """, status_data)

            self.connection.commit()
            self._cache['system_status'].clear()

        except Exception as e:
            logger.error("刷新系统状态失败", error=str(e))

    async def _flush_performance(self) -> None:
        """刷新性能指标到数据库"""
        if not self._cache['performance']:
            return

        try:
            cursor = self.connection.cursor()
            for metrics in self._cache['performance']:
                metrics_data = metrics.to_dict()

                cursor.execute("""
                    INSERT OR REPLACE INTO performance_metrics (
                        timestamp, period, total_trades, successful_trades, failed_trades, success_rate,
                        total_profit, total_loss, net_profit, profit_factor,
                        sharpe_ratio, max_drawdown, calmar_ratio,
                        avg_trade_duration_seconds, avg_profit_per_trade, avg_execution_time_ms
                    ) VALUES (
                        :timestamp, :period, :total_trades, :successful_trades, :failed_trades, :success_rate,
                        :total_profit, :total_loss, :net_profit, :profit_factor,
                        :sharpe_ratio, :max_drawdown, :calmar_ratio,
                        :avg_trade_duration_seconds, :avg_profit_per_trade, :avg_execution_time_ms
                    )
                """, metrics_data)

            self.connection.commit()
            self._cache['performance'].clear()

        except Exception as e:
            logger.error("刷新性能指标失败", error=str(e))

    async def _auto_commit_task(self) -> None:
        """自动提交任务"""
        while True:
            try:
                await asyncio.sleep(self.auto_commit_interval)

                # 刷新所有缓存
                await self._flush_trades()
                await self._flush_prices()
                await self._flush_spreads()
                await self._flush_risk_metrics()
                await self._flush_system_status()
                await self._flush_performance()

            except Exception as e:
                logger.error("自动提交失败", error=str(e))

    async def close(self) -> None:
        """关闭数据库连接"""
        try:
            # 刷新所有缓存
            await self._flush_trades()
            await self._flush_prices()
            await self._flush_spreads()
            await self._flush_risk_metrics()
            await self._flush_system_status()
            await self._flush_performance()

            # 关闭连接
            if self.connection:
                self.connection.close()

            logger.info("数据库连接已关闭")

        except Exception as e:
            logger.error("关闭数据库失败", error=str(e))