"""
Web监控应用

提供套利交易系统的实时监控界面
"""

import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request, Query
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import structlog
import asyncio

from ..utils.time_utils import TimeUtils

logger = structlog.get_logger(__name__)


class WebSocketManager:
    """WebSocket连接管理器"""

    def __init__(self):
        self.active_connections: list[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info("WebSocket连接建立", total_connections=len(self.active_connections))

    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info("WebSocket连接断开", total_connections=len(self.active_connections))

    async def send_personal_message(self, message: str, websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error("发送WebSocket消息失败", error=str(e))

    async def broadcast(self, message: str):
        """广播消息给所有连接"""
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error("广播WebSocket消息失败", error=str(e))
                disconnected.append(connection)

        # 清理断开的连接
        for connection in disconnected:
            self.disconnect(connection)


def create_web_app(engine) -> FastAPI:
    """
    创建Web应用

    Args:
        engine: 套利引擎实例

    Returns:
        FastAPI应用实例
    """
    app = FastAPI(
        title="Binance-Lighter 套利交易监控",
        description="实时监控套利交易系统状态",
        version="1.0.0"
    )

    # 设置模板和静态文件
    templates = Jinja2Templates(directory="src/web/templates")
    app.mount("/static", StaticFiles(directory="src/web/static"), name="static")

    # WebSocket管理器
    manager = WebSocketManager()

    # JSON序列化辅助函数
    def serialize_for_json(obj):
        """将对象序列化为JSON兼容格式"""
        from ..arbitrage.strategy import ArbitrageSignal, SignalType, OrderInfo
        from enum import Enum

        if obj is None:
            return None
        elif isinstance(obj, ArbitrageSignal):
            # 专门处理ArbitrageSignal对象
            return {
                'signal_type': obj.signal_type.value if obj.signal_type else None,
                'binance_price': obj.binance_price,
                'lighter_price': obj.lighter_price,
                'diff_rate': obj.diff_rate,
                'ma_value': obj.ma_value,
                'confidence': obj.confidence,
                'timestamp': obj.timestamp,
                'reason': obj.reason,
                'expected_profit': obj.expected_profit,
                'spread_pct': obj.spread_pct
            }
        elif isinstance(obj, OrderInfo):
            # 专门处理OrderInfo对象
            return {
                'exchange': obj.exchange,
                'order_id': obj.order_id,
                'symbol': obj.symbol,
                'side': obj.side,
                'amount': obj.amount,
                'price': obj.price,
                'status': obj.status,
                'timestamp': obj.timestamp,
                'trade_id': obj.trade_id
            }
        elif isinstance(obj, Enum):
            # 处理枚举类型
            return obj.value
        elif hasattr(obj, '__dict__'):
            # 处理数据类对象
            result = {}
            for key, value in obj.__dict__.items():
                result[key] = serialize_for_json(value)
            return result
        elif isinstance(obj, (list, tuple)):
            return [serialize_for_json(item) for item in obj]
        elif isinstance(obj, dict):
            return {k: serialize_for_json(v) for k, v in obj.items()}
        else:
            return obj

    # 设置引擎的Web回调函数（实现即时推送）
    def on_price_update(price_data):
        """价格更新回调函数"""
        if len(manager.active_connections) > 0:
            try:
                # 序列化价格数据
                serialized_data = {}
                for key, value in price_data.items():
                    serialized_data[key] = serialize_for_json(value)

                # 安全地创建异步任务
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        loop.create_task(manager.broadcast(json.dumps({
                            "type": "price_update",
                            "data": serialized_data,
                            "timestamp": time.time()
                        })))
                except RuntimeError:
                    # 如果没有运行的事件循环，跳过推送
                    pass
            except Exception as e:
                logger.debug("即时价格推送失败", error=str(e))

    def on_status_update(status_data):
        """状态更新回调函数"""
        if len(manager.active_connections) > 0:
            try:
                # 序列化状态数据
                serialized_data = serialize_for_json(status_data)

                # 安全地创建异步任务
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        loop.create_task(manager.broadcast(json.dumps({
                            "type": "status_update",
                            "data": serialized_data,
                            "timestamp": time.time()
                        })))
                except RuntimeError:
                    # 如果没有运行的事件循环，跳过推送
                    pass
            except Exception as e:
                logger.debug("即时状态推送失败", error=str(e))

    def on_log_update(level: str, message: str, **kwargs):
        """日志更新回调函数"""
        if len(manager.active_connections) > 0:
            try:
                # 添加日志到缓存
                add_log_entry(level, message, **kwargs)
            except Exception as e:
                logger.debug("即时日志推送失败", error=str(e))

    def on_trade_update(trade_data):
        """交易记录更新回调函数"""
        if len(manager.active_connections) > 0:
            try:
                # 序列化交易数据
                serialized_data = serialize_for_json(trade_data)

                # 安全地创建异步任务
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        loop.create_task(manager.broadcast(json.dumps({
                            "type": "trade_update",
                            "data": serialized_data,
                            "timestamp": time.time()
                        })))
                except RuntimeError:
                    # 如果没有运行的事件循环，跳过推送
                    pass
            except Exception as e:
                logger.debug("即时交易推送失败", error=str(e))

    # 将回调函数注册到引擎
    if engine and hasattr(engine, 'set_web_callbacks'):
        engine.set_web_callbacks(
            price_callback=on_price_update,
            status_callback=on_status_update,
            log_callback=on_log_update,
            trade_callback=on_trade_update
        )
        logger.info("Web回调函数已设置")

        # 同时设置交易记录器的回调函数
        if hasattr(engine, 'trade_recorder') and engine.trade_recorder:
            engine.trade_recorder.set_web_callback(on_trade_update)
            logger.info("交易记录器Web回调函数已设置")

        # 立即添加一些初始日志
        on_log_update("INFO", "Web监控界面已启动")
        on_log_update("INFO", f"系统运行模式: {'模拟交易' if engine.is_paper_trading else '实盘交易'}")
        on_log_update("INFO", f"监控交易对: {engine.config.get('trading', {}).get('symbol', 'BTC/USDT')}")

    @app.get("/", response_class=HTMLResponse)
    async def dashboard(request: Request):
        """主监控页面"""
        return templates.TemplateResponse("dashboard.html", {"request": request})

    @app.get("/test-time", response_class=HTMLResponse)
    async def test_time_display():
        """时间显示测试页面"""
        import os
        file_path = os.path.join(os.getcwd(), "test_time_display.html")
        if not os.path.exists(file_path):
            # 尝试相对路径
            file_path = "test_time_display.html"

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return HTMLResponse(content=f.read())
        except FileNotFoundError:
            return HTMLResponse(content="<h1>测试页面未找到</h1><p>请确保 test_time_display.html 文件存在</p>", status_code=404)

    @app.get("/api/status")
    async def get_status():
        """获取系统状态API"""
        try:
            if not engine:
                return {"error": "引擎未初始化"}

            # 确保统计数据已从数据库加载
            if hasattr(engine, '_stats_loaded') and not engine._stats_loaded:
                await engine._load_stats_from_database()

            status = engine.get_status()

            # 添加时间戳和连接状态
            status['timestamp'] = time.time()

            # 确保所有必要的字段都存在
            if 'connection_status' not in status:
                status['connection_status'] = engine.connection_status
            if 'is_trading_enabled' not in status:
                status['is_trading_enabled'] = engine.is_trading_enabled
            if 'is_paper_trading' not in status:
                status['is_paper_trading'] = engine.is_paper_trading

            logger.debug("返回状态数据",
                       total_trades=status['stats']['total_trades'],
                       total_profit=status['stats']['total_profit'])

            return status

        except Exception as e:
            logger.error("获取系统状态失败", error=str(e))
            return {"error": str(e)}

    @app.get("/api/prices")
    async def get_prices():
        """获取价格数据API"""
        try:
            if not engine or not engine.strategy:
                return {"error": "引擎或策略未初始化"}

            strategy_status = engine.strategy.get_strategy_status()

            # 序列化当前信号
            current_signal = strategy_status.get('current_signal')
            serialized_signal = serialize_for_json(current_signal) if current_signal else None

            return {
                "binance_prices": strategy_status.get('binance_prices', {}),
                "lighter_prices": strategy_status.get('lighter_prices', {}),
                "spread_ma_value": strategy_status.get('spread_ma_value'),
                "current_signal": serialized_signal,
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error("获取价格数据失败", error=str(e))
            return {"error": str(e)}

    @app.get("/api/trades")
    async def get_trades(limit: int = Query(100, description="返回记录数量")):
        """获取交易历史API"""
        try:
            if not engine or not engine.db_manager:
                return {"error": "引擎或数据库未初始化"}

            # 从数据库获取最近的交易记录
            trades = await engine.db_manager.get_recent_trades(limit)

            # 获取统计信息
            stats = engine.stats

            return {
                "trades": trades,
                "total_trades": stats.get('total_trades', 0),
                "successful_trades": stats.get('successful_trades', 0),
                "failed_trades": stats.get('failed_trades', 0),
                "total_profit": stats.get('total_profit', 0.0),
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error("获取交易历史失败", error=str(e))
            return {"error": str(e)}

    @app.get("/api/trades/today")
    async def get_today_trades():
        """获取今日交易记录API"""
        try:
            if not engine or not engine.db_manager:
                return {"error": "引擎或数据库未初始化"}

            # 获取今日交易记录
            today = TimeUtils.now_local()
            trades = await engine.db_manager.get_trades_by_date(today)

            return {
                "trades": trades,
                "date": today.isoformat(),
                "count": len(trades),
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error("获取今日交易记录失败", error=str(e))
            return {"error": str(e)}

    @app.get("/api/prices/history")
    async def get_price_history(
        symbol: str = Query("BTC/USDT", description="交易对"),
        exchange: str = Query("binance", description="交易所"),
        hours: int = Query(24, description="历史小时数")
    ):
        """获取价格历史数据API"""
        try:
            if not engine or not engine.db_manager:
                return {"error": "引擎或数据库未初始化"}

            # 从数据库获取价格历史
            price_history = await engine.db_manager.get_price_history(symbol, exchange, hours)

            return {
                "symbol": symbol,
                "exchange": exchange,
                "hours": hours,
                "data": price_history,
                "count": len(price_history),
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error("获取价格历史失败", error=str(e))
            return {"error": str(e)}

    @app.get("/api/spreads/history")
    async def get_spread_history(
        symbol: str = Query("BTC/USDT", description="交易对"),
        hours: int = Query(24, description="历史小时数")
    ):
        """获取价差历史数据API"""
        try:
            if not engine or not engine.db_manager:
                return {"error": "引擎或数据库未初始化"}

            # 从数据库获取价差历史
            spread_history = await engine.db_manager.get_spread_history(symbol, hours)

            return {
                "symbol": symbol,
                "hours": hours,
                "data": spread_history,
                "count": len(spread_history),
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error("获取价差历史失败", error=str(e))
            return {"error": str(e)}

    @app.get("/api/risk")
    async def get_risk_status():
        """获取风险状态API"""
        try:
            if not engine or not engine.risk_manager:
                return {"error": "引擎或风险管理器未初始化"}

            risk_status = engine.risk_manager.get_risk_status()
            risk_metrics = engine.risk_manager.get_risk_metrics()

            # 获取最新的风险指标（如果有数据库的话）
            latest_risk_metrics = None
            if engine.db_manager:
                latest_risk_metrics = await engine.db_manager.get_latest_risk_metrics()

            return {
                "risk_status": risk_status,
                "risk_metrics": risk_metrics,
                "latest_risk_metrics": latest_risk_metrics,
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error("获取风险状态失败", error=str(e))
            return {"error": str(e)}

    @app.get("/api/performance")
    async def get_performance_metrics():
        """获取性能指标API"""
        try:
            if not engine or not engine.trade_recorder:
                return {"error": "引擎或交易记录器未初始化"}

            # 计算今日性能指标
            today = TimeUtils.now_local()
            daily_performance = await engine.trade_recorder.calculate_daily_performance(today)

            # 获取活跃交易数量
            active_trades = await engine.trade_recorder.get_active_trades()

            return {
                "daily_performance": daily_performance.to_dict(),
                "active_trades_count": len(active_trades),
                "active_trades": [trade.to_dict() for trade in active_trades],
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error("获取性能指标失败", error=str(e))
            return {"error": str(e)}

    @app.post("/api/emergency_stop")
    async def emergency_stop():
        """紧急停止API"""
        try:
            if not engine or not engine.risk_manager:
                return {"error": "引擎或风险管理器未初始化"}

            engine.risk_manager.trigger_emergency_stop("用户手动触发")
            engine.is_trading_enabled = False

            # 广播紧急停止消息
            await manager.broadcast(json.dumps({
                "type": "emergency_stop",
                "message": "紧急停止已触发",
                "timestamp": time.time()
            }))

            return {"success": True, "message": "紧急停止已触发"}

        except Exception as e:
            logger.error("紧急停止失败", error=str(e))
            return {"error": str(e)}

    @app.post("/api/reset_emergency")
    async def reset_emergency():
        """重置紧急停止API"""
        try:
            if not engine or not engine.risk_manager:
                return {"error": "引擎或风险管理器未初始化"}

            engine.risk_manager.reset_emergency_stop()
            engine.is_trading_enabled = True

            # 广播重置消息
            await manager.broadcast(json.dumps({
                "type": "emergency_reset",
                "message": "紧急停止已重置",
                "timestamp": time.time()
            }))

            return {"success": True, "message": "紧急停止已重置"}

        except Exception as e:
            logger.error("重置紧急停止失败", error=str(e))
            return {"error": str(e)}

    @app.post("/api/toggle_trading")
    async def toggle_trading():
        """切换交易状态API"""
        try:
            if not engine:
                return {"error": "引擎未初始化"}

            engine.is_trading_enabled = not engine.is_trading_enabled

            # 广播状态变更消息
            await manager.broadcast(json.dumps({
                "type": "trading_status_changed",
                "is_trading_enabled": engine.is_trading_enabled,
                "message": f"交易已{'启用' if engine.is_trading_enabled else '禁用'}",
                "timestamp": time.time()
            }))

            return {
                "success": True,
                "is_trading_enabled": engine.is_trading_enabled,
                "message": f"交易已{'启用' if engine.is_trading_enabled else '禁用'}"
            }

        except Exception as e:
            logger.error("切换交易状态失败", error=str(e))
            return {"error": str(e)}

    @app.get("/api/system_info")
    async def get_system_info():
        """获取系统信息API"""
        try:
            import psutil

            return {
                "cpu_usage": psutil.cpu_percent(),
                "memory_usage": psutil.virtual_memory().percent,
                "disk_usage": psutil.disk_usage('/').percent,
                "system_uptime": time.time() - engine.stats.get('start_time', time.time()),
                "python_version": f"{psutil.Process().memory_info().rss / 1024 / 1024:.1f} MB",
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error("获取系统信息失败", error=str(e))
            return {"error": str(e)}

    # 日志缓存
    log_buffer = []
    max_log_entries = 1000

    @app.get("/api/logs")
    async def get_logs(limit: int = Query(100, description="返回日志条数")):
        """获取系统日志API"""
        try:
            # 如果日志缓存为空，添加一些初始日志
            if not log_buffer:
                current_time = time.time()
                log_buffer.append({
                    "level": "INFO",
                    "message": "系统日志初始化完成",
                    "timestamp": current_time,
                    "data": {}
                })
                log_buffer.append({
                    "level": "INFO",
                    "message": f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    "timestamp": current_time,
                    "data": {}
                })
                if engine:
                    log_buffer.append({
                        "level": "INFO",
                        "message": f"引擎状态: {'运行中' if engine.is_running else '已停止'}",
                        "timestamp": current_time,
                        "data": {}
                    })

            # 返回最近的日志条目
            recent_logs = log_buffer[-limit:] if log_buffer else []

            return {
                "logs": recent_logs,
                "total_count": len(log_buffer),
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error("获取系统日志失败", error=str(e))
            return {"error": str(e)}

    def add_log_entry(level: str, message: str, **kwargs):
        """添加日志条目到缓存"""
        global log_buffer

        log_entry = {
            "timestamp": time.time(),
            "level": level,
            "message": message,
            "data": kwargs
        }

        log_buffer.append(log_entry)

        # 保持缓存大小
        if len(log_buffer) > max_log_entries:
            log_buffer = log_buffer[-max_log_entries:]

        # 实时推送日志到WebSocket客户端
        if len(manager.active_connections) > 0:
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(manager.broadcast(json.dumps({
                        "type": "log_update",
                        "data": log_entry,
                        "timestamp": time.time()
                    })))
            except RuntimeError:
                pass

    @app.websocket("/ws")
    async def websocket_endpoint(websocket: WebSocket):
        """WebSocket端点，用于实时数据推送"""
        await manager.connect(websocket)

        try:
            while True:
                # 等待客户端消息
                data = await websocket.receive_text()

                try:
                    message = json.loads(data)
                    message_type = message.get('type')

                    if message_type == 'subscribe':
                        # 客户端订阅实时数据
                        await websocket.send_text(json.dumps({
                            "type": "subscription_confirmed",
                            "message": "已订阅实时数据",
                            "timestamp": time.time()
                        }))

                        # 立即发送当前状态数据
                        try:
                            if engine:
                                # 发送当前状态
                                status = engine.get_status()
                                if status:
                                    # 移除价格数据，因为有独立的价格推送
                                    if 'strategy_status' in status:
                                        strategy_status = status['strategy_status'].copy()
                                        if 'binance_prices' in strategy_status:
                                            del strategy_status['binance_prices']
                                        if 'lighter_prices' in strategy_status:
                                            del strategy_status['lighter_prices']
                                        status['strategy_status'] = strategy_status

                                    await websocket.send_text(json.dumps({
                                        "type": "status_update",
                                        "data": status,
                                        "timestamp": time.time()
                                    }))

                                # 发送当前价格数据
                                if engine.strategy:
                                    strategy_status = engine.strategy.get_strategy_status()
                                    price_data = {
                                        "binance_prices": strategy_status.get('binance_prices', {}),
                                        "lighter_prices": strategy_status.get('lighter_prices', {}),
                                        "spread_ma_value": strategy_status.get('spread_ma_value'),
                                        "current_signal": strategy_status.get('current_signal'),
                                        "timestamp": time.time()
                                    }
                                    await websocket.send_text(json.dumps({
                                        "type": "price_update",
                                        "data": price_data,
                                        "timestamp": time.time()
                                    }))

                                # 发送连接状态
                                connection_status = {
                                    "binance_connected": engine.connection_status.get('binance', False),
                                    "lighter_connected": engine.connection_status.get('lighter', False),
                                    "websocket_connected": engine.connection_status.get('websocket', False),
                                    "is_running": engine.is_running,
                                    "is_trading_enabled": engine.is_trading_enabled,
                                    "timestamp": time.time()
                                }
                                await websocket.send_text(json.dumps({
                                    "type": "connection_status",
                                    "data": connection_status,
                                    "timestamp": time.time()
                                }))

                        except Exception as e:
                            logger.error("发送初始状态失败", error=str(e))

                    elif message_type == 'ping':
                        # 心跳检测
                        await websocket.send_text(json.dumps({
                            "type": "pong",
                            "timestamp": time.time()
                        }))

                except json.JSONDecodeError:
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "message": "无效的JSON格式",
                        "timestamp": time.time()
                    }))

        except WebSocketDisconnect:
            manager.disconnect(websocket)
        except Exception as e:
            logger.error("WebSocket处理异常", error=str(e))
            manager.disconnect(websocket)

    # 定期推送数据的后台任务
    # 数据缓存
    last_status_data = None
    last_price_data = None
    status_update_interval = 2  # 状态更新间隔（秒）- 更频繁的更新
    price_update_interval = 1   # 价格更新间隔（秒）

    async def broadcast_price_updates():
        """高频广播价格更新数据"""
        global last_price_data

        # 确保变量初始化
        if 'last_price_data' not in globals():
            last_price_data = None

        while True:
            try:
                if engine and len(manager.active_connections) > 0:
                    # 获取价格数据
                    try:
                        if engine.strategy:
                            strategy_status = engine.strategy.get_strategy_status()

                            current_price_data = {
                                "binance_prices": strategy_status.get('binance_prices', {}),
                                "lighter_prices": strategy_status.get('lighter_prices', {}),
                                "spread_ma_value": strategy_status.get('spread_ma_value'),
                                "current_signal": strategy_status.get('current_signal'),
                                "timestamp": time.time()
                            }

                            # 总是推送价格数据（高频更新）
                            await manager.broadcast(json.dumps({
                                "type": "price_update",
                                "data": current_price_data,
                                "timestamp": time.time()
                            }))

                            # 更新缓存数据
                            last_price_data = current_price_data.copy()
                            logger.debug("广播价格更新",
                                       connections=len(manager.active_connections),
                                       binance_last=current_price_data.get('binance_prices', {}).get('last'),
                                       lighter_last=current_price_data.get('lighter_prices', {}).get('last'))

                    except Exception as e:
                        logger.error("获取价格数据失败", error=str(e))

                await asyncio.sleep(price_update_interval)  # 每1秒更新价格

            except Exception as e:
                logger.error("广播价格更新失败", error=str(e))
                await asyncio.sleep(5)

    async def broadcast_status_updates():
        """定期广播系统状态更新数据"""
        global last_status_data

        # 确保变量初始化
        if 'last_status_data' not in globals():
            last_status_data = None

        # 首次推送初始状态
        initial_push = True

        while True:
            try:
                if engine and len(manager.active_connections) > 0:
                    # 获取最新状态（不包括价格数据，减少数据量）
                    status = engine.get_status()

                    # 移除价格数据，因为有独立的价格推送
                    if 'strategy_status' in status:
                        strategy_status = status['strategy_status'].copy()
                        # 保留非价格的策略状态
                        if 'binance_prices' in strategy_status:
                            del strategy_status['binance_prices']
                        if 'lighter_prices' in strategy_status:
                            del strategy_status['lighter_prices']
                        status['strategy_status'] = strategy_status

                    # 定期推送状态数据（不再比较变化，因为统计数据经常变化）
                    await manager.broadcast(json.dumps({
                        "type": "status_update",
                        "data": status,
                        "timestamp": time.time()
                    }))
                    last_status_data = status.copy() if status else None
                    initial_push = False
                    logger.debug("广播状态更新",
                               connections=len(manager.active_connections),
                               is_running=status.get('is_running'),
                               total_trades=status.get('stats', {}).get('total_trades'))

                await asyncio.sleep(status_update_interval)  # 每5秒更新状态

            except Exception as e:
                logger.error("广播状态更新失败", error=str(e))
                await asyncio.sleep(10)  # 出错时等待更长时间

    async def broadcast_connection_status():
        """定期广播连接状态"""
        while True:
            try:
                if engine and len(manager.active_connections) > 0:
                    connection_status = {
                        "binance_connected": engine.connection_status.get('binance', False),
                        "lighter_connected": engine.connection_status.get('lighter', False),
                        "websocket_connected": engine.connection_status.get('websocket', False),
                        "is_running": engine.is_running,
                        "is_trading_enabled": engine.is_trading_enabled,
                        "timestamp": time.time()
                    }

                    await manager.broadcast(json.dumps({
                        "type": "connection_status",
                        "data": connection_status,
                        "timestamp": time.time()
                    }))

                await asyncio.sleep(3)  # 每3秒更新连接状态

            except Exception as e:
                logger.error("广播连接状态失败", error=str(e))
                await asyncio.sleep(10)

    # 启动后台任务
    @app.on_event("startup")
    async def startup_event():
        """应用启动事件"""
        # 启动多个推送任务
        asyncio.create_task(broadcast_price_updates())    # 高频价格更新
        asyncio.create_task(broadcast_status_updates())   # 系统状态更新
        asyncio.create_task(broadcast_connection_status()) # 连接状态更新
        logger.info("Web应用启动完成 - 已启动实时数据推送服务")

    @app.on_event("shutdown")
    async def shutdown_event():
        """应用关闭事件"""
        logger.info("Web应用正在关闭")

    return app